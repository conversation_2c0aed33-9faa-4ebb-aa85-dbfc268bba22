"""
Analyze node for advanced search workflow.
"""

from typing import Dict, Any, Optional

from ..state import SearchState
from ....shared.base.node_base import BaseNode
from ....shared.services import ContextProcessor


class AnalyzeNode(BaseNode):
    """Node for analyzing and processing sources."""
    
    def __init__(self):
        super().__init__("analyze")
        self.context_processor = ContextProcessor()
    
    async def execute(
        self, 
        state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Analyze phase - process and score content relevance."""
        self.log_entry(state)
        
        try:
            sources = state.get("scraped_sources", [])
            search_queries = state.get("search_queries", [])
            
            # Convert sources to dict format for context processor
            source_dicts = []
            for source in sources:
                source_dicts.append({
                    "title": source.title,
                    "url": source.url,
                    "content": source.content,
                    "score": source.score,
                    "published_date": source.published_date
                })
            
            # Process sources using context processor
            processed_sources = await self.context_processor.process_sources(
                query=state["query"],
                sources=source_dicts,
                search_queries=search_queries
            )
            
            result = {
                "processed_sources": processed_sources,
                "phase": "synthesizing"
            }
            
            self.log_exit(state, result)
            return result
            
        except Exception as error:
            self.logger.error(f"Analyze phase failed: {str(error)}")
            return {
                "error": str(error),
                "error_type": "llm",
                "phase": "error"
            }
