"""
Error handler node for advanced search workflow.
"""

from typing import Dict, Any, Optional

from ..state import SearchState
from ....shared.base.node_base import BaseNode
from ....shared.config import SEARCH_CONFIG


class ErrorHandlerNode(BaseNode):
    """Node for handling errors and retries."""
    
    def __init__(self):
        super().__init__("error_handler")
    
    async def execute(
        self, 
        state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Handle errors and determine retry strategy."""
        self.log_entry(state)
        
        try:
            retry_count = state.get("retry_count", 0)
            max_retries = state.get("max_retries", SEARCH_CONFIG["MAX_RETRIES"])
            error_type = state.get("error_type", "unknown")
            
            # Increment retry count
            retry_count += 1
            
            # Determine if we should retry
            if retry_count <= max_retries:
                self.logger.info(f"Retrying after {error_type} error (attempt {retry_count})")
                
                # Reset to appropriate phase based on error type
                if error_type == "search":
                    next_phase = "searching"
                elif error_type == "scrape":
                    next_phase = "analyzing"  # Skip scraping
                else:
                    next_phase = "understanding"  # Start over for LLM errors
                
                result = {
                    "retry_count": retry_count,
                    "phase": next_phase,
                    "error": None,  # Clear error
                    "error_type": None
                }
            else:
                self.logger.error(f"Max retries exceeded for {error_type} error")
                result = {
                    "retry_count": retry_count,
                    "phase": "error",
                    "final_answer": f"I apologize, but I encountered an error while processing your request: {state.get('error', 'Unknown error')}"
                }
            
            self.log_exit(state, result)
            return result
            
        except Exception as error:
            self.logger.error(f"Error handler failed: {str(error)}")
            return {
                "phase": "error",
                "final_answer": "I apologize, but I encountered a critical error while processing your request."
            }
