"""
State definition for advanced search workflow.
"""

from typing import List, Dict, Any, Optional
from typing_extensions import TypedDict

from ...shared.base.state_types import BaseState
from ...shared.models.events import SearchPhase, ErrorType
from ...shared.models.sources import Source, SubQuery, ProcessedSource


class SearchState(BaseState):
    """State for advanced search workflow."""
    query: str
    context: Optional[List[Dict[str, str]]]
    
    # Understanding phase
    understanding: Optional[str]
    
    # Planning phase
    search_queries: Optional[List[str]]
    current_search_index: int
    sub_queries: Optional[List[SubQuery]]
    search_attempt: int
    
    # Search phase
    sources: List[Source]
    scraped_sources: List[Source]
    
    # Analysis phase
    processed_sources: Optional[List[ProcessedSource]]
    
    # Synthesis phase
    final_answer: Optional[str]
    follow_up_questions: Optional[List[str]]
    
    # Workflow control
    phase: SearchPhase
    error_type: Optional[ErrorType]
