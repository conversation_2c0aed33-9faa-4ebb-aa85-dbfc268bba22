"""
Abstract base class for LangGraph workflows.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, TypeVar, Generic
from langgraph.graph.state import CompiledStateGraph

from ..services import LLMService
from ..utils import SessionManager, ErrorHandler

StateType = TypeVar('StateType', bound=Dict[str, Any])


class BaseWorkflow(ABC, Generic[StateType]):
    """Abstract base class for all LangGraph workflows."""
    
    def __init__(self, options: Optional[Dict[str, Any]] = None):
        self.options = options or {}
        self.llm_service = LLMService()
        self.session_manager = SessionManager()
        self.error_handler = ErrorHandler(self.__class__.__name__)
        
        self.graph = self._build_graph()
    
    @abstractmethod
    def _build_graph(self) -> CompiledStateGraph:
        """Build the LangGraph workflow. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    async def execute(
        self, 
        initial_state: StateType, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute the workflow. Must be implemented by subclasses."""
        pass
    
    def get_workflow_name(self) -> str:
        """Get the name of this workflow."""
        return self.__class__.__name__
    
    def get_supported_operations(self) -> list[str]:
        """Get list of operations this workflow supports."""
        return ["execute"]
