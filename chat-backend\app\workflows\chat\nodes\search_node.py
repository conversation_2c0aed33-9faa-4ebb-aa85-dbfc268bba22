"""
Search node for simple chat workflow.
"""

from typing import Dict, Any, Optional
from langgraph.config import get_stream_writer

from ..state import ChatState
from ....shared.base.node_base import BaseNode
from ....shared.services.search_providers import TavilySearchProvider


class SearchNode(BaseNode):
    """Node for performing web search using <PERSON>ly."""
    
    def __init__(self):
        super().__init__("search")
        self.tavily_provider = TavilySearchProvider()
    
    async def execute(
        self, 
        state: ChatState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Perform web search using Tavily."""
        self.log_entry(state)
        
        try:
            writer = get_stream_writer()
            writer({"status": "Calling Tavily...", "step": "search_start"})
            
            user_query = state["messages"][-1].content
            
            # Use the Tavily provider (sync call)
            search_response = self.tavily_provider.search(
                query=user_query,
                limit=4
            )
            
            results_list = []
            for result in search_response.get("results", []):
                results_list.append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", "")
                })
            
            writer({
                "status": f"Found {len(results_list)} sources", 
                "step": "search_complete", 
                "results_count": len(results_list)
            })
            
            result = {
                "messages": state["messages"],
                "session_id": state["session_id"],
                "search_performed": True,
                "search_query": user_query,
                "search_results": results_list,
                "needs_search": False
            }
            
            self.log_exit(state, result)
            return result
            
        except Exception as e:
            self.logger.error(f"Search failed: {str(e)}")
            writer = get_stream_writer()
            writer({"status": "Search failed", "step": "search_error"})
            
            error_result = {
                "messages": state["messages"],
                "session_id": state["session_id"],
                "search_performed": False,
                "search_query": None,
                "search_results": [],
                "needs_search": False
            }
            
            return error_result
