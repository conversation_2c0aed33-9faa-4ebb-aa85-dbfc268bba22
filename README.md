# AI Chat Application

A modern chat application built with React/Vite frontend and Python/LangGraph backend that enables users to chat with AI.

> This is a dummy change for testing purposes.

## Architecture

- **Frontend**: React with TypeScript, Vite, Tailwind CSS, and shadcn/ui components
- **Backend**: FastAPI with Lang<PERSON>rap<PERSON> for AI conversation management
- **AI Integration**: OpenAI GPT-3.5-turbo via LangChain

## Features

- 🎨 Modern, responsive chat interface
- 💬 Real-time messaging with AI
- 🔍 **Web search integration with <PERSON>ly** - Automatically searches the web for current information
- 📊 **Search status indicators** - Shows "Calling Tavily..." during web searches
- 🔗 **Source display** - Shows search results with clickable links and content previews
- 📝 Session management and chat history
- 🔄 Message persistence during session
- ⚡ Fast development with hot reload
- 🎯 Error handling and loading states

## Prerequisites

- Python 3.12+
- Node.js 18+
- OpenAI API key
- Tavily API key (for web search functionality)

## Setup Instructions

### 1. Backend Setup

```bash
cd chat-backend
poetry install
```

### 2. Configure API Keys

Edit `chat-backend/.env` and add your API keys:

```env
OPENAI_API_KEY=your_actual_openai_api_key_here
TAVILY_API_KEY=your_actual_tavily_api_key_here
```

Get your Tavily API key from: https://tavily.com/

### 3. Frontend Setup

```bash
cd chat-frontend
npm install
```

## Running the Application

### Start Backend Server

```bash
cd chat-backend
poetry run fastapi dev app/main.py
```

The backend will be available at: http://localhost:8000
API documentation: http://localhost:8000/docs

### Start Frontend Server

```bash
cd chat-frontend
npm run dev
```

The frontend will be available at: http://localhost:5173

## API Endpoints

- `POST /chat` - Send a message and get AI response
- `GET /chat/history/{session_id}` - Get chat history for a session
- `DELETE /chat/history/{session_id}` - Clear chat history for a session
- `GET /chat/sessions` - Get list of active sessions
- `GET /healthz` - Health check endpoint

## Usage

1. Open http://localhost:5173 in your browser
2. Type a message in the input field
3. Press Enter or click the Send button
4. The AI will respond using LangGraph processing
5. **For web search queries** (containing keywords like "current", "latest", "news", "today", "weather", etc.):
   - You'll see "Calling Tavily..." status indicator
   - Search results will appear below the AI response with source links
6. Use "Clear Chat" button to start a new conversation

### Web Search Examples
- "What's the latest news about AI?"
- "Current weather in New York"
- "Recent developments in technology"
- "What happened today in the world?"

## Technology Stack

### Frontend
- React 18 with TypeScript
- Vite for fast development
- Tailwind CSS for styling
- shadcn/ui for UI components
- Lucide React for icons

### Backend
- FastAPI for REST API
- LangGraph for conversation flow with conditional routing
- LangChain for AI integration
- OpenAI GPT-3.5-turbo
- **Tavily for web search integration**
- Pydantic for data validation
- Python-dotenv for environment variables

## Development Notes

- The application uses in-memory storage for chat sessions (data is lost on server restart)
- CORS is configured to allow all origins for development
- Both servers support hot reload during development
- Session management is handled automatically by the backend

## Troubleshooting

### "Sorry, I encountered an error" message
- Check that your OpenAI API key is correctly set in `chat-backend/.env`
- Verify the backend server is running on port 8000
- Check the browser console and backend logs for detailed error messages

### Frontend not connecting to backend
- Ensure both servers are running
- Check that the API URL in `chat-frontend/.env` matches the backend URL
- Verify CORS configuration in the backend

### Installation issues
- Make sure you have the correct Python and Node.js versions
- Try deleting `node_modules` and running `npm install` again
- For Python issues, try `poetry install --no-cache`
