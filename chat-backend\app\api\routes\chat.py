"""
Chat-related API routes.
"""

import uuid
import json
from datetime import datetime
from typing import List, Dict, Any
from fastapi import API<PERSON>outer, HTTPException, Depends
from fastapi.responses import StreamingResponse
from langchain_core.messages import HumanMessage, AIMessage

from ...shared.models.base import <PERSON>t<PERSON>essage, ChatResponse, ChatHistory, SearchResult
from ...shared.utils import Session<PERSON>ana<PERSON>, StreamingUtils
from ...workflows.chat.state import ChatState
from ...workflows.chat.workflow import SimpleChatWorkflow
from ..dependencies import get_session_manager, get_simple_chat_workflow

router = APIRouter(prefix="/chat", tags=["chat"])


@router.post("", response_model=ChatResponse)
async def chat_endpoint(
    chat_message: ChatMessage,
    session_manager: SessionManager = Depends(get_session_manager),
    workflow: SimpleChatWorkflow = Depends(get_simple_chat_workflow)
):
    """Send a message and get AI response."""
    try:
        session_id = chat_message.session_id or str(uuid.uuid4())
        
        # Get existing messages or create new session
        messages = session_manager.get_session(session_id)
        
        # Add user message
        user_message = HumanMessage(content=chat_message.message)
        session_manager.add_message(session_id, user_message)
        messages.append(user_message)
        
        # Create workflow state
        state = ChatState(
            messages=messages.copy(),
            session_id=session_id,
            search_performed=False,
            search_query=None,
            search_results=[],
            needs_search=False,
            retry_count=0,
            max_retries=2
        )
        
        # Execute workflow
        result = await workflow.execute(state)
        
        # Update session with result messages
        session_manager.update_session(session_id, result["messages"])
        
        # Extract AI response
        ai_response = result["messages"][-1].content
        
        # Convert search results
        search_results = []
        if result.get("search_results"):
            search_results = [
                SearchResult(
                    title=r["title"],
                    url=r["url"],
                    content=r["content"]
                ) for r in result["search_results"]
            ]
        
        return ChatResponse(
            response=ai_response,
            session_id=session_id,
            search_performed=result.get("search_performed", False),
            search_query=result.get("search_query"),
            search_results=search_results
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")


@router.get("/history/{session_id}", response_model=ChatHistory)
async def get_chat_history(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """Get chat history for a session."""
    if not session_manager.session_exists(session_id):
        raise HTTPException(status_code=404, detail="Session not found")
    
    messages = session_manager.get_session(session_id)
    
    formatted_messages = []
    for msg in messages:
        if isinstance(msg, HumanMessage):
            formatted_messages.append({
                "role": "user", 
                "content": msg.content, 
                "timestamp": datetime.now().isoformat()
            })
        elif isinstance(msg, AIMessage):
            formatted_messages.append({
                "role": "assistant", 
                "content": msg.content, 
                "timestamp": datetime.now().isoformat()
            })
    
    return ChatHistory(messages=formatted_messages, session_id=session_id)


@router.delete("/history/{session_id}")
async def clear_chat_history(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """Clear chat history for a session."""
    if session_manager.clear_session(session_id):
        return {"message": "Chat history cleared"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")


@router.get("/sessions")
async def get_active_sessions(
    session_manager: SessionManager = Depends(get_session_manager)
):
    """Get list of active chat sessions."""
    return {"sessions": session_manager.get_active_sessions()}


@router.post("/stream")
async def chat_stream_endpoint(
    chat_message: ChatMessage,
    session_manager: SessionManager = Depends(get_session_manager),
    workflow: SimpleChatWorkflow = Depends(get_simple_chat_workflow)
):
    """Stream chat responses with progressive updates."""
    async def generate_stream():
        try:
            session_id = chat_message.session_id or str(uuid.uuid4())

            # Get existing messages or create new session
            messages = session_manager.get_session(session_id)

            # Add user message
            user_message = HumanMessage(content=chat_message.message)
            session_manager.add_message(session_id, user_message)
            messages.append(user_message)

            # Create workflow state
            state = ChatState(
                messages=messages.copy(),
                session_id=session_id,
                search_performed=False,
                search_query=None,
                search_results=[],
                needs_search=False,
                retry_count=0,
                max_retries=2
            )

            final_result = None
            async for chunk in workflow.stream(state):
                if isinstance(chunk, tuple):
                    mode, data = chunk
                    if mode == "custom":
                        if data.get("type") == "content":
                            yield StreamingUtils.format_content_event(data.get('data'))
                        else:
                            yield StreamingUtils.format_status_event(data.get('status', ''), data.get('step'))
                    elif mode == "updates":
                        for node_name, node_result in data.items():
                            serializable_result = {}
                            for key, value in node_result.items():
                                if key == "messages":
                                    serializable_result[key] = StreamingUtils.serialize_messages(value)
                                else:
                                    serializable_result[key] = value
                            yield StreamingUtils.format_sse_data({
                                'type': 'node_complete',
                                'node': node_name,
                                'data': serializable_result
                            })
                        final_result = data

            # Update session with final result
            if final_result:
                for node_data in final_result.values():
                    if node_data.get("messages"):
                        session_manager.update_session(session_id, node_data["messages"])
                        break

            # Generate final response
            messages = session_manager.get_session(session_id)
            ai_response = messages[-1].content if messages else "No response generated"

            search_results = []
            search_performed = False
            search_query = None

            if final_result:
                for node_data in final_result.values():
                    if node_data.get("search_results"):
                        search_results = [
                            SearchResult(
                                title=r["title"],
                                url=r["url"],
                                content=r["content"]
                            ) for r in node_data["search_results"]
                        ]
                        search_performed = node_data.get("search_performed", False)
                        search_query = node_data.get("search_query")
                        break

            final_response = ChatResponse(
                response=ai_response,
                session_id=session_id,
                search_performed=search_performed,
                search_query=search_query,
                search_results=search_results
            )

            yield StreamingUtils.format_final_event(final_response)
            yield StreamingUtils.format_done_event()

        except Exception as e:
            yield StreamingUtils.format_error_event(f"Chat error: {str(e)}")

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )
