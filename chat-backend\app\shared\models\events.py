"""
Event models for workflow progress tracking.
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any, Literal

SearchPhase = Literal[
    'understanding', 'planning', 'searching', 'analyzing', 'synthesizing', 'complete', 'error'
]

ErrorType = Literal['search', 'scrape', 'llm', 'unknown']


@dataclass
class SearchEvent:
    """Search event for progress tracking."""
    type: str
    phase: Optional[SearchPhase] = None
    message: Optional[str] = None
    query: Optional[str] = None
    index: Optional[int] = None
    total: Optional[int] = None
    sources: Optional[List[Dict[str, Any]]] = None
    url: Optional[str] = None
    title: Optional[str] = None
    stage: Optional[str] = None
    summary: Optional[str] = None
    chunk: Optional[str] = None
    content: Optional[str] = None
    follow_up_questions: Optional[List[str]] = None
    error: Optional[str] = None
    error_type: Optional[ErrorType] = None
