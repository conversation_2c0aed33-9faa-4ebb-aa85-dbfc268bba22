"""
Simple chat workflow with optional search capability.
"""

from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph

from .state import ChatState
from .nodes import ChatNode, SearchNode, should_search
from ...shared.base.workflow_base import BaseWorkflow


class SimpleChatWorkflow(BaseWorkflow[ChatState]):
    """Simple chat workflow with conditional search routing."""
    
    def __init__(self, options: Optional[Dict[str, Any]] = None):
        self.chat_node = ChatNode()
        self.search_node = SearchNode()
        super().__init__(options)
    
    def _build_graph(self) -> CompiledStateGraph:
        """Build the simple chat workflow graph."""
        workflow = StateGraph(ChatState)
        
        # Add nodes
        workflow.add_node("chat", self.chat_node.execute)
        workflow.add_node("search", self.search_node.execute)
        
        # Set entry point
        workflow.set_entry_point("chat")
        
        # Add conditional routing
        workflow.add_conditional_edges(
            "chat",
            should_search,
            {
                "search": "search",
                "chat": END
            }
        )
        
        # Search always goes to END
        workflow.add_edge("search", END)
        
        return workflow.compile()
    
    async def execute(
        self, 
        initial_state: ChatState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute the simple chat workflow."""
        return self.graph.invoke(initial_state, config)
    
    async def stream(
        self, 
        initial_state: ChatState, 
        config: Optional[Dict[str, Any]] = None
    ):
        """Stream the workflow execution."""
        async for chunk in self.graph.astream(initial_state, config, stream_mode=["updates", "custom"]):
            yield chunk
    
    def get_supported_operations(self) -> list[str]:
        """Get list of operations this workflow supports."""
        return ["execute", "stream"]
