"""
Synthesize node for advanced search workflow.
"""

from typing import Dict, Any, Optional
from langchain_core.messages import SystemMessage, HumanMessage

from ..state import SearchState
from ....shared.base.node_base import BaseNode
from ....shared.services import LLMService


class SynthesizeNode(BaseNode):
    """Node for generating comprehensive streaming answer."""
    
    def __init__(self):
        super().__init__("synthesize")
        self.llm_service = LLMService()
    
    async def execute(
        self, 
        state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Synthesize phase - generate comprehensive answer."""
        self.log_entry(state)
        
        try:
            processed_sources = state.get("processed_sources", [])
            query = state["query"]
            
            # Build context from processed sources
            context_parts = []
            for i, source in enumerate(processed_sources[:5], 1):  # Top 5 sources
                context_parts.append(
                    f"Source {i}: {source.title}\n{source.content[:500]}...\nURL: {source.url}\n"
                )
            
            context = "\n".join(context_parts)
            
            # Generate comprehensive answer
            messages = [
                SystemMessage(content=f"""You are a research assistant providing comprehensive answers based on search results.

Use the following sources to provide a detailed, accurate answer to the user's question.

Sources:
{context}

Instructions:
1. Provide a comprehensive answer based on the sources
2. Include specific facts, data, and details from the sources
3. Cite sources when making specific claims
4. If sources conflict, acknowledge the different perspectives
5. Be objective and factual"""),
                HumanMessage(content=f"Question: {query}")
            ]
            
            llm = self.llm_service.get_quality_llm()
            response = await llm.ainvoke(messages)
            
            result = {
                "final_answer": response.content,
                "phase": "complete"
            }
            
            self.log_exit(state, result)
            return result
            
        except Exception as error:
            self.logger.error(f"Synthesize phase failed: {str(error)}")
            return {
                "error": str(error),
                "error_type": "llm",
                "phase": "error"
            }
