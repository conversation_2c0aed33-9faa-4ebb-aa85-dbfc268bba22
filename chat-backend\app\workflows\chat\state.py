"""
State definition for simple chat workflow.
"""

from typing import List, Dict, Any, Optional
from typing_extensions import TypedDict
from langchain_core.messages import BaseMessage

from ...shared.base.state_types import BaseState


class ChatState(BaseState):
    """State for simple chat workflow."""
    messages: List[BaseMessage]
    search_performed: bool
    search_query: Optional[str]
    search_results: List[Dict[str, Any]]
    needs_search: bool
