"""
Planning node for advanced search workflow.
"""

import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from langchain_core.messages import SystemMessage, HumanMessage

from ..state import SearchState
from ....shared.base.node_base import BaseNode
from ....shared.services import LLMService
from ....shared.config import SEARCH_CONFIG
from ....shared.models.sources import SubQuery


class PlanNode(BaseNode):
    """Node for generating search strategies and sub-queries."""
    
    def __init__(self):
        super().__init__("plan")
        self.llm_service = LLMService()
    
    async def execute(
        self, 
        state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Planning phase - generate search strategies."""
        self.log_entry(state)
        
        try:
            sub_queries = state.get("sub_queries")
            if not sub_queries:
                extracted = await self._extract_sub_queries(state["query"])
                sub_queries = [
                    SubQuery(
                        query=sq["question"],
                        reasoning=sq["searchQuery"],
                        priority=1
                    )
                    for sq in extracted
                ]
            
            # For now, use simple search queries from sub-queries
            search_queries = [sq.query for sq in sub_queries]
            
            result = {
                "search_queries": search_queries,
                "sub_queries": sub_queries,
                "current_search_index": 0,
                "phase": "searching"
            }
            
            self.log_exit(state, result)
            return result
            
        except Exception as error:
            self.logger.error(f"Planning phase failed: {str(error)}")
            return {
                "error": str(error),
                "error_type": "llm",
                "phase": "error"
            }
    
    async def _extract_sub_queries(self, query: str) -> List[Dict[str, str]]:
        """Extract sub-queries from the main query."""
        messages = [
            SystemMessage(content=f"""Break down this search query into 2-4 focused sub-questions that would help provide a comprehensive answer.

{self._get_current_date_context()}

For each sub-question, provide:
1. A clear, specific question
2. An optimized search query for that question

Format as JSON array with objects containing 'question' and 'searchQuery' fields.

Example:
[
  {{"question": "What is X?", "searchQuery": "X definition explanation"}},
  {{"question": "How does X work?", "searchQuery": "X mechanism process how it works"}}
]"""),
            HumanMessage(content=query)
        ]
        
        llm = self.llm_service.get_fast_llm()
        response = await llm.ainvoke(messages)
        try:
            return json.loads(response.content)
        except:
            return [{"question": query, "searchQuery": query}]
    
    def _get_current_date_context(self) -> str:
        """Get current date context for queries."""
        now = datetime.now()
        return f"Today is {now.strftime('%A, %B %d, %Y')}. Current time: {now.strftime('%I:%M %p')} UTC."
