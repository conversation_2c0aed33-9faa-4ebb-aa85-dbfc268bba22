"""
Routing logic for simple chat workflow.
"""

from langchain_core.messages import HumanMessage

from ..state import ChatState
from ....shared.services import LLMService


def should_search(state: ChatState) -> str:
    """Use LLM to determine if web search is needed."""
    if state.get("search_performed", False):
        return "chat"
    
    last_message = state["messages"][-1].content
    
    # Get LLM for decision making
    llm_service = LLMService()
    llm = llm_service.get_chat_llm()
    
    # Use LLM to decide if search is needed
    decision_prompt = f"""
    Analyze this user message and determine if it requires current/real-time information that would need a web search.
    
    User message: "{last_message}"
    
    Return "SEARCH" if the query needs current information (stock prices, news, weather, recent events, etc.)
    Return "CHAT" if it can be answered with general knowledge (basic facts, explanations, math, etc.)
    
    Examples:
    - "What's NVIDIA's current market cap?" -> SEARCH (needs current data)
    - "Where is Paris?" -> CHAT (basic geography)
    - "Latest AI news" -> SEARCH (needs current info)
    - "Explain quantum physics" -> CHAT (general knowledge)
    
    Response:"""
    
    decision = llm.invoke([HumanMessage(content=decision_prompt)]).content.strip()
    
    if "SEARCH" in decision.upper():
        return "search"
    else:
        return "chat"
