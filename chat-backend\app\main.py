"""
FastAPI application with reorganized LangGraph workflows.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from .shared.config import APP_SETTINGS
from .api import chat_router, search_router, health_router

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="AI Chat Application",
    description="A modern chat application with LangGraph workflows",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=APP_SETTINGS["CORS_ORIGINS"],
    allow_credentials=APP_SETTINGS["CORS_CREDENTIALS"],
    allow_methods=APP_SETTINGS["CORS_METHODS"],
    allow_headers=APP_SETTINGS["CORS_HEADERS"],
)

# Include routers
app.include_router(health_router)
app.include_router(chat_router)
app.include_router(search_router)


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AI Chat Application API",
        "version": "1.0.0",
        "workflows": ["simple_chat", "advanced_search"]
    }
