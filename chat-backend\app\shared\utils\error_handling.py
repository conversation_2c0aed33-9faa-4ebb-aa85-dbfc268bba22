"""
Common error handling patterns for workflows.
"""

import logging
from typing import Dict, Any, Optional
from enum import Enum

from ..models.events import ErrorType


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorHandler:
    """Common error handling utilities."""
    
    def __init__(self, logger_name: str = "workflow"):
        self.logger = logging.getLogger(logger_name)
    
    def handle_search_error(
        self, 
        error: Exception, 
        query: str, 
        provider: str = "unknown"
    ) -> Dict[str, Any]:
        """Handle search-related errors."""
        error_msg = f"Search failed for query '{query}' using {provider}: {str(error)}"
        self.logger.error(error_msg)
        
        return {
            "error": str(error),
            "error_type": "search",
            "query": query,
            "provider": provider,
            "severity": ErrorSeverity.MEDIUM.value
        }
    
    def handle_llm_error(
        self, 
        error: Exception, 
        operation: str = "llm_call"
    ) -> Dict[str, Any]:
        """Handle LLM-related errors."""
        error_msg = f"LLM error during {operation}: {str(error)}"
        self.logger.error(error_msg)
        
        return {
            "error": str(error),
            "error_type": "llm",
            "operation": operation,
            "severity": ErrorSeverity.HIGH.value
        }
    
    def handle_scraping_error(
        self, 
        error: Exception, 
        url: str
    ) -> Dict[str, Any]:
        """Handle web scraping errors."""
        error_msg = f"Scraping failed for URL '{url}': {str(error)}"
        self.logger.warning(error_msg)
        
        return {
            "error": str(error),
            "error_type": "scrape",
            "url": url,
            "severity": ErrorSeverity.LOW.value
        }
    
    def should_retry(
        self, 
        error_info: Dict[str, Any], 
        retry_count: int, 
        max_retries: int = 3
    ) -> bool:
        """Determine if an operation should be retried."""
        if retry_count >= max_retries:
            return False
        
        error_type = error_info.get("error_type")
        severity = error_info.get("severity", ErrorSeverity.MEDIUM.value)
        
        # Don't retry critical errors
        if severity == ErrorSeverity.CRITICAL.value:
            return False
        
        # Retry search and scraping errors
        if error_type in ["search", "scrape"]:
            return True
        
        # Be more conservative with LLM errors
        if error_type == "llm" and retry_count < 2:
            return True
        
        return False
    
    def log_retry_attempt(
        self, 
        operation: str, 
        retry_count: int, 
        error_info: Dict[str, Any]
    ) -> None:
        """Log retry attempt."""
        self.logger.info(
            f"Retrying {operation} (attempt {retry_count + 1}): {error_info.get('error', 'Unknown error')}"
        )
