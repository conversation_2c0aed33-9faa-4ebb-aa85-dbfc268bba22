"""
Chat node for simple chat workflow.
"""

from typing import Dict, Any, Optional
from langgraph.config import get_stream_writer
from langchain_core.messages import AIMessage, SystemMessage

from ..state import ChatState
from ....shared.base.node_base import BaseNode
from ....shared.services import LLMService


class ChatNode(BaseNode):
    """Node for processing chat messages and generating AI responses."""
    
    def __init__(self):
        super().__init__("chat")
        self.llm_service = LLMService()
    
    async def execute(
        self, 
        state: ChatState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process the chat messages and generate AI response."""
        self.log_entry(state)
        
        try:
            writer = get_stream_writer()
            writer({"status": "AI is thinking...", "step": "chat_start"})
            
            messages = state["messages"].copy()
            
            # Add search context if available
            if state.get("search_performed", False) and state.get("search_results"):
                writer({"status": "Processing search results...", "step": "context_processing"})
                search_context = "\n\nBased on recent web search results:\n"
                for i, result in enumerate(state["search_results"], 1):
                    search_context += f"{i}. {result['title']}\n   {result['content'][:200]}...\n   Source: {result['url']}\n\n"
                
                context_message = SystemMessage(
                    content=f"You have access to current web search results. Use this information to provide an accurate and up-to-date response.{search_context}"
                )
                messages.insert(-1, context_message)
            
            writer({"status": "Generating response...", "step": "llm_generating"})
            
            # Get streaming LLM
            llm = self.llm_service.get_chat_llm(streaming=True)
            
            full_response = ""
            for chunk in llm.stream(messages):
                if chunk.content:
                    full_response += chunk.content
                    writer({"type": "content", "data": chunk.content})
            
            ai_message = AIMessage(content=full_response)
            messages.append(ai_message)
            
            writer({"status": "Response complete", "step": "chat_complete"})
            
            result = {
                "messages": messages,
                "session_id": state["session_id"],
                "search_performed": state.get("search_performed", False),
                "search_query": state.get("search_query"),
                "search_results": state.get("search_results", []),
                "needs_search": False
            }
            
            self.log_exit(state, result)
            return result
            
        except Exception as e:
            return self.handle_error(e, state)
