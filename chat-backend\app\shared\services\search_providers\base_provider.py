"""
Abstract base class for search providers.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional


class BaseSearchProvider(ABC):
    """Abstract base class for search providers."""
    
    @abstractmethod
    def search(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Perform a search query.
        
        Args:
            query: The search query string
            limit: Maximum number of results to return
            
        Returns:
            Dict containing search results
        """
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """Get the name of this search provider."""
        pass
