"""
Base class for workflow nodes.
"""

import logging
from typing import Dict, Any, Optional, TypeVar
from abc import ABC, abstractmethod

from ..utils import ErrorHandler

StateType = TypeVar('StateType', bound=Dict[str, Any])


class BaseNode(ABC):
    """Base class for all workflow nodes."""
    
    def __init__(self, node_name: str):
        self.node_name = node_name
        self.logger = logging.getLogger(f"node.{node_name}")
        self.error_handler = ErrorHandler(f"node.{node_name}")
    
    @abstractmethod
    async def execute(
        self, 
        state: StateType, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute the node logic. Must be implemented by subclasses."""
        pass
    
    def log_entry(self, state: StateType) -> None:
        """Log node entry."""
        session_id = state.get("session_id", "unknown")
        self.logger.info(f"Entering {self.node_name} for session {session_id}")
    
    def log_exit(self, state: StateType, result: Dict[str, Any]) -> None:
        """Log node exit."""
        session_id = state.get("session_id", "unknown")
        self.logger.info(f"Exiting {self.node_name} for session {session_id}")
    
    def handle_error(self, error: Exception, state: StateType) -> Dict[str, Any]:
        """Handle node errors."""
        error_info = self.error_handler.handle_llm_error(error, self.node_name)
        
        return {
            **state,
            "error": error_info["error"],
            "error_type": error_info["error_type"]
        }
