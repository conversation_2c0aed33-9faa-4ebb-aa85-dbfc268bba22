"""
Tavily search provider implementation.
"""

import os
from typing import Dict, Any
from tavily import TavilyC<PERSON>

from .base_provider import BaseSearchProvider


class TavilySearchProvider(BaseSearchProvider):
    """Tavily search provider implementation."""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("TAVILY_API_KEY")
        self.client = None

    def _ensure_client(self):
        """Ensure Tavily client is initialized."""
        if self.client is None:
            if not self.api_key:
                raise ValueError("TAVILY_API_KEY environment variable is not set")
            self.client = TavilyClient(api_key=self.api_key)
    
    def search(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Perform search using Tavily API.
        
        Args:
            query: The search query string
            limit: Maximum number of results to return
            
        Returns:
            Dict containing search results in standardized format
        """
        try:
            self._ensure_client()
            search_results = self.client.search(
                query=query,
                search_depth="basic",
                max_results=limit
            )
            
            results_list = []
            for result in search_results.get("results", []):
                results_list.append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0),
                    "published_date": result.get("published_date", "")
                })
            
            return {
                "results": results_list,
                "query": query,
                "provider": "tavily"
            }
            
        except Exception as e:
            return {
                "results": [],
                "query": query,
                "provider": "tavily",
                "error": str(e)
            }
    
    def get_provider_name(self) -> str:
        """Get the name of this search provider."""
        return "tavily"
