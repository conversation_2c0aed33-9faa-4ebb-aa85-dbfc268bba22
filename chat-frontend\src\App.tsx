import { useState, useRef, useEffect } from 'react'
import { Send, MessageCircle, Trash2, Search, ExternalLink, Settings } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import SearchProgressSidebar from '@/components/SearchProgressSidebar'
import { SearchEvent, SearchPhase, Source } from '@/types/search'

interface SearchResult {
  title: string
  url: string
  content: string
}

interface Message {
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  search_performed?: boolean
  search_query?: string
  search_results?: SearchResult[]
}


function App() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  const [currentStatus, setCurrentStatus] = useState<string>('')
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [streamingMessage, setStreamingMessage] = useState('')
  const [searchMode, setSearchMode] = useState<'simple' | 'advanced'>('simple')
  const [searchEvents, setSearchEvents] = useState<SearchEvent[]>([])
  const [currentPhase, setCurrentPhase] = useState<SearchPhase>('understanding')
  const [searchSources, setSearchSources] = useState<Source[]>([])
  const [searchError, setSearchError] = useState<string | undefined>()
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const eventSourceRef = useRef<EventSource | null>(null)

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }
    }
  }, [])

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    }

    setMessages(prev => [...prev, userMessage])
    const messageToSend = inputMessage
    setInputMessage('')
    setIsLoading(true)
    setCurrentStatus('Connecting...')
    setStreamingMessage('')
    
    if (searchMode === 'advanced') {
      setSearchEvents([])
      setCurrentPhase('understanding')
      setSearchSources([])
      setSearchError(undefined)
      setFollowUpQuestions([])
    }

    try {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }

      const endpoint = searchMode === 'advanced' ? '/chat/advanced-stream' : '/chat/stream'
      const response = await fetch(`${API_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageToSend,
          session_id: sessionId
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error('No response body')
      }

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            
            if (data === '[DONE]') {
              setIsLoading(false)
              setCurrentStatus('')
              setIsSearching(false)
              inputRef.current?.focus()
              return
            }

            try {
              const parsed = JSON.parse(data)
              
              if (parsed.type === 'search_event' && searchMode === 'advanced') {
                const event: SearchEvent = parsed.data
                setSearchEvents(prev => [...prev, event])
                
                if (event.type === 'phase-update') {
                  setCurrentPhase(event.phase)
                  setIsSearching(event.phase !== 'complete' && event.phase !== 'error')
                }
                
                if (event.type === 'found' && event.sources) {
                  setSearchSources(prev => [...prev, ...event.sources])
                }
                
                if (event.type === 'error') {
                  setSearchError(event.error)
                  setCurrentPhase('error')
                  setIsSearching(false)
                }
              } else if (parsed.type === 'status') {
                setCurrentStatus(parsed.data.status)
                if (parsed.data.step === 'search_start') {
                  setIsSearching(true)
                } else if (parsed.data.step === 'search_complete' || parsed.data.step === 'search_error') {
                  setIsSearching(false)
                }
              } else if (parsed.type === 'node_complete') {
                console.log(`Node ${parsed.node} completed:`, parsed.data)
              } else if (parsed.type === 'content') {
                setStreamingMessage(prev => prev + parsed.data)
              } else if (parsed.type === 'final') {
                const finalData = parsed.data
                
                console.log('Final data received:', finalData)
                console.log('Search performed:', finalData.search_performed)
                console.log('Search results:', finalData.search_results)
                
                if (!sessionId) {
                  setSessionId(finalData.session_id)
                }

                const aiMessage: Message = {
                  role: 'assistant',
                  content: streamingMessage || finalData.response,
                  timestamp: new Date().toISOString(),
                  search_performed: finalData.search_performed,
                  search_query: finalData.search_query,
                  search_results: finalData.search_results
                }

                console.log('AI message created:', aiMessage)
                setMessages(prev => [...prev, aiMessage])
                setStreamingMessage('')
                
                if (searchMode === 'advanced' && finalData.follow_up_questions) {
                  setFollowUpQuestions(finalData.follow_up_questions)
                }
                
                if (searchMode === 'advanced') {
                  setCurrentPhase('complete')
                  setIsSearching(false)
                }
              } else if (parsed.type === 'error') {
                throw new Error(parsed.message)
              }
            } catch (parseError) {
              console.error('Error parsing stream data:', parseError)
            }
          }
        }
      }

    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date().toISOString()
      }
      setMessages(prev => [...prev, errorMessage])
      
      setIsLoading(false)
      setCurrentStatus('')
      setIsSearching(false)
      setStreamingMessage('')
      inputRef.current?.focus()
    }
  }

  const clearChat = async () => {
    if (sessionId) {
      try {
        await fetch(`${API_URL}/chat/history/${sessionId}`, {
          method: 'DELETE'
        })
      } catch (error) {
        console.error('Error clearing chat history:', error)
      }
    }
    setMessages([])
    setSessionId(null)
    inputRef.current?.focus()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className={`${searchMode === 'advanced' ? 'max-w-7xl' : 'max-w-4xl'} mx-auto h-[calc(100vh-2rem)]`}>
        <div className="flex gap-4 h-full">
          <Card className={`${searchMode === 'advanced' ? 'flex-1' : 'w-full'} flex flex-col shadow-xl`}>
          <CardHeader className="border-b bg-white/50 backdrop-blur-sm flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageCircle className="h-6 w-6 text-blue-600" />
                <CardTitle className="text-xl font-semibold text-gray-800">
                  AI Chat Assistant
                </CardTitle>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-gray-600" />
                  <Label htmlFor="search-mode" className="text-sm font-medium">
                    Advanced Search
                  </Label>
                  <Switch
                    id="search-mode"
                    checked={searchMode === 'advanced'}
                    onCheckedChange={(checked) => setSearchMode(checked ? 'advanced' : 'simple')}
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearChat}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear Chat
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="flex-1 flex flex-col p-0 min-h-0">
            <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-lg font-medium">Welcome to AI Chat!</p>
                    <p className="text-sm">Start a conversation by typing a message below.</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message, index) => {
                    console.log('Message render check:', {
                      index,
                      search_performed: message.search_performed,
                      search_results: message.search_results,
                      search_results_length: message.search_results?.length
                    });
                    
                    return (
                    <div
                      key={index}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] rounded-lg px-4 py-2 ${
                          message.role === 'user'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-100 text-gray-800 border'
                        }`}
                      >
                        <p className="whitespace-pre-wrap break-words overflow-wrap-anywhere">{message.content}</p>
                        {message.search_performed && message.search_results && message.search_results.length > 0 && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <div className="flex items-center gap-1 mb-2">
                              <Search className="w-3 h-3 text-blue-600" />
                              <span className="text-xs font-medium text-blue-600">
                                Found {message.search_results.length} sources
                              </span>
                            </div>
                            <div className="space-y-2">
                              {message.search_results.map((result, idx) => (
                                <div key={idx} className="text-xs bg-blue-50 rounded p-2">
                                  <div className="flex items-start gap-2">
                                    <ExternalLink className="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" />
                                    <div className="flex-1 min-w-0 overflow-hidden">
                                      <a 
                                        href={result.url} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="font-medium text-blue-700 hover:text-blue-800 block truncate"
                                      >
                                        {result.title}
                                      </a>
                                      <p className="text-gray-600 mt-1 line-clamp-2 break-words">
                                        {result.content.substring(0, 120)}...
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        <p
                          className={`text-xs mt-1 ${
                            message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                          }`}
                        >
                          {formatTime(message.timestamp)}
                        </p>
                      </div>
                    </div>
                    );
                  })}
                  
                  {/* Follow-up Questions for Advanced Mode */}
                  {searchMode === 'advanced' && followUpQuestions.length > 0 && !isLoading && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="text-sm font-medium text-blue-800 mb-2">Suggested follow-up questions:</h4>
                      <div className="space-y-2">
                        {followUpQuestions.map((question, idx) => (
                          <button
                            key={idx}
                            onClick={() => {
                              setInputMessage(question)
                              setFollowUpQuestions([])
                            }}
                            className="block w-full text-left text-sm text-blue-700 hover:text-blue-900 hover:bg-blue-100 p-2 rounded transition-colors"
                          >
                            {question}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-gray-100 border rounded-lg px-4 py-2 max-w-[80%]">
                        {streamingMessage ? (
                          <div>
                            <p className="whitespace-pre-wrap break-words overflow-wrap-anywhere">{streamingMessage}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                              <span className="text-xs text-gray-500">
                                {isSearching ? (
                                  <>
                                    <Search className="inline w-3 h-3 mr-1" />
                                    {currentStatus || 'Calling Tavily...'}
                                  </>
                                ) : (
                                  currentStatus || 'AI is typing...'
                                )}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                            <span className="text-sm text-gray-500">
                              {isSearching ? (
                                <>
                                  <Search className="inline w-3 h-3 mr-1" />
                                  {currentStatus || 'Calling Tavily...'}
                                </>
                              ) : (
                                currentStatus || 'AI is typing...'
                              )}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </ScrollArea>
            
            <div className="border-t bg-white/50 backdrop-blur-sm p-4 flex-shrink-0">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message here..."
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button
                  onClick={sendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  className="px-4"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
          </Card>
          
          {/* Advanced Search Progress Sidebar */}
          {searchMode === 'advanced' && (
            <SearchProgressSidebar
              isVisible={true}
              events={searchEvents}
              currentPhase={currentPhase}
              sources={searchSources}
              isSearching={isSearching}
              error={searchError}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default App
