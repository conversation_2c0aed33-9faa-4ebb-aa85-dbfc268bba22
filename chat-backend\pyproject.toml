[tool.poetry]
name = "app"
version = "0.1.0"
description = ""
authors = ["Devin AI <158243242+devin-ai-integration[bot]@users.noreply.github.com>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
fastapi = {extras = ["standard"], version = "^0.116.1"}
psycopg = {extras = ["binary"], version = "^3.2.9"}
langgraph = "^0.5.4"
langchain-openai = "^0.3.28"
langchain-core = "^0.3.72"
python-dotenv = "^1.1.1"
tavily-python = "^0.7.10"
requests = "^2.31.0"
aiohttp = "^3.9.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
