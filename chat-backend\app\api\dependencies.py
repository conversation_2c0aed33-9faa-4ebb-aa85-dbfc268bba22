"""
FastAPI dependencies.
"""

from ..shared.utils import <PERSON><PERSON><PERSON><PERSON>
from ..workflows import SimpleChatWorkflow, AdvancedSearchWorkflow

# Global instances - lazy loaded
_session_manager = None
_simple_chat_workflow = None
_advanced_search_workflow = None


def get_session_manager() -> Session<PERSON>anager:
    """Get session manager dependency."""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager


def get_simple_chat_workflow() -> SimpleChatWorkflow:
    """Get simple chat workflow dependency."""
    global _simple_chat_workflow
    if _simple_chat_workflow is None:
        _simple_chat_workflow = SimpleChatWorkflow()
    return _simple_chat_workflow


def get_advanced_search_workflow() -> AdvancedSearchWorkflow:
    """Get advanced search workflow dependency."""
    global _advanced_search_workflow
    if _advanced_search_workflow is None:
        _advanced_search_workflow = AdvancedSearchWorkflow()
    return _advanced_search_workflow
