"""
Routing logic for advanced search workflow.
"""

from ..state import SearchState


def search_routing(state: SearchState) -> str:
    """Route from search phase."""
    if state.get("phase") == "error":
        return "handle_error"
    return "scrape"


def scrape_routing(state: SearchState) -> str:
    """Route from scrape phase."""
    if state.get("phase") == "error":
        return "handle_error"
    return "analyze"


def analyze_routing(state: SearchState) -> str:
    """Route from analyze phase."""
    if state.get("phase") == "error":
        return "handle_error"
    return "synthesize"


def synthesize_routing(state: SearchState) -> str:
    """Route from synthesize phase."""
    if state.get("phase") == "error":
        return "handle_error"
    return "complete"


def error_routing(state: SearchState) -> str:
    """Route from error handling node."""
    retry_count = state.get("retry_count", 0)
    max_retries = state.get("max_retries", 2)
    
    if retry_count < max_retries:
        error_type = state.get("error_type")
        if error_type == "search":
            return "search"
        elif error_type == "scrape":
            return "analyze"  # Skip scraping on retry
        else:
            return "understand"  # Start over for LLM errors
    
    return "complete"  # Max retries exceeded
