"""
Advanced search-related API routes.
"""

import uuid
import json
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from langchain_core.messages import HumanMessage, AIMessage

from ...shared.models.base import ChatMessage, ChatResponse, SearchResult
from ...shared.models.events import SearchEvent
from ...shared.utils import SessionManager, StreamingUtils
from ...workflows.search.state import SearchState
from ...workflows.search.workflow import AdvancedSearchWorkflow
from ...shared.config import SEARCH_CONFIG
from ..dependencies import get_session_manager, get_advanced_search_workflow

router = APIRouter(prefix="/search", tags=["search"])


@router.post("/advanced-stream")
async def advanced_search_stream_endpoint(
    chat_message: ChatMessage,
    session_manager: SessionManager = Depends(get_session_manager),
    workflow: AdvancedSearchWorkflow = Depends(get_advanced_search_workflow)
):
    """Stream advanced search responses with detailed progress tracking."""
    async def generate_advanced_stream():
        try:
            session_id = chat_message.session_id or str(uuid.uuid4())
            
            # Get existing messages for context
            messages = session_manager.get_session(session_id)
            
            # Build context from previous exchanges
            context = []
            for i in range(0, len(messages) - 1, 2):
                if i + 1 < len(messages):
                    user_msg = messages[i]
                    ai_msg = messages[i + 1]
                    if isinstance(user_msg, HumanMessage) and isinstance(ai_msg, AIMessage):
                        context.append({
                            "query": user_msg.content,
                            "response": ai_msg.content
                        })
            
            # Create workflow state
            state = SearchState(
                query=chat_message.message,
                context=context[-3:] if context else None,  # Last 3 exchanges
                current_search_index=0,
                search_attempt=0,
                sources=[],
                scraped_sources=[],
                phase="understanding",
                retry_count=0,
                max_retries=SEARCH_CONFIG["MAX_RETRIES"],
                session_id=session_id
            )
            
            final_result = None
            async for chunk in workflow.stream(state):
                if isinstance(chunk, tuple):
                    mode, data = chunk
                    if mode == "custom":
                        # Handle custom events (progress updates)
                        yield StreamingUtils.format_sse_data({
                            'type': 'search_event', 
                            'data': data
                        })
                    elif mode == "updates":
                        for node_name, node_result in data.items():
                            # Serialize the node result
                            serializable_result = {}
                            for key, value in node_result.items():
                                if key == "sources" or key == "scraped_sources":
                                    # Convert Source objects to dicts
                                    serializable_result[key] = [
                                        {
                                            "title": s.title,
                                            "url": s.url,
                                            "content": s.content[:200] + "..." if len(s.content) > 200 else s.content,
                                            "score": s.score
                                        } for s in value
                                    ]
                                elif key == "processed_sources":
                                    # Convert ProcessedSource objects to dicts
                                    serializable_result[key] = [
                                        {
                                            "title": s.title,
                                            "url": s.url,
                                            "content": s.content[:200] + "..." if len(s.content) > 200 else s.content,
                                            "relevance_score": s.relevance_score
                                        } for s in (value or [])
                                    ]
                                else:
                                    serializable_result[key] = value
                            
                            yield StreamingUtils.format_sse_data({
                                'type': 'node_complete', 
                                'node': node_name, 
                                'data': serializable_result
                            })
                        final_result = data
            
            # Add user message to session
            user_message = HumanMessage(content=chat_message.message)
            session_manager.add_message(session_id, user_message)
            
            # Extract final answer and create AI message
            ai_response = "I apologize, but I couldn't generate a response."
            search_results = []
            
            if final_result:
                for node_data in final_result.values():
                    if node_data.get("final_answer"):
                        ai_response = node_data["final_answer"]
                    
                    # Convert sources to search results
                    if node_data.get("sources"):
                        search_results = [
                            SearchResult(
                                title=s.title if hasattr(s, 'title') else s.get('title', ''),
                                url=s.url if hasattr(s, 'url') else s.get('url', ''),
                                content=s.content if hasattr(s, 'content') else s.get('content', '')
                            ) for s in node_data["sources"][:5]  # Top 5 sources
                        ]
            
            # Add AI response to session
            ai_message = AIMessage(content=ai_response)
            session_manager.add_message(session_id, ai_message)
            
            # Create final response
            final_response = ChatResponse(
                response=ai_response,
                session_id=session_id,
                search_performed=len(search_results) > 0,
                search_query=chat_message.message,
                search_results=search_results
            )
            
            yield StreamingUtils.format_final_event(final_response)
            yield StreamingUtils.format_done_event()
            
        except Exception as e:
            yield StreamingUtils.format_error_event(f"Advanced search error: {str(e)}")
    
    return StreamingResponse(
        generate_advanced_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )
