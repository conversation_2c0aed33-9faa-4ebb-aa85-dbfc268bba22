"""
Context processor for advanced search engine.
Based on the TypeScript context-processor.ts file.
Handles source processing, relevance scoring, and content summarization.
"""

import asyncio
import re
from typing import List, Dict, Any, Optional, Callable

from ..models.sources import ProcessedSource
from .llm_service import LLMService


class ContextProcessor:
    """
    Process sources for optimal context selection with relevance scoring
    and AI-powered summarization.
    """
    
    def __init__(self):
        self.MAX_TOTAL_CHARS = 100000
        self.MIN_CHARS_PER_SOURCE = 2000
        self.MAX_CHARS_PER_SOURCE = 15000
        self.CONTEXT_WINDOW_SIZE = 500  # chars before/after keyword match
        
        self.llm_service = LLMService()
        self.openai_client = self.llm_service.get_async_openai_client()
    
    async def process_sources(
        self,
        query: str,
        sources: List[Dict[str, Any]],
        search_queries: List[str],
        on_progress: Optional[Callable[[str, Optional[str]], None]] = None
    ) -> List[ProcessedSource]:
        """
        Process sources for optimal context selection.
        
        Args:
            query: The original user query
            sources: List of source dictionaries from search results
            search_queries: List of generated search queries
            on_progress: Optional callback for progress updates
            
        Returns:
            List of processed sources sorted by relevance
        """
        summary_length = self._calculate_summary_length(len(sources))
        
        processed_sources = await asyncio.gather(*[
            self._summarize_source(source, query, search_queries, summary_length, on_progress)
            for source in sources
        ])
        
        valid_sources = [
            s for s in processed_sources 
            if s.relevance_score > 0
        ]
        valid_sources.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return valid_sources
    
    def _extract_keywords(self, query: str, search_queries: List[str]) -> List[str]:
        """
        Extract keywords from query and search queries.
        
        Args:
            query: The original user query
            search_queries: List of generated search queries
            
        Returns:
            List of unique keywords
        """
        all_text = " ".join([query] + search_queries).lower()
        
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 
            'with', 'by', 'from', 'as', 'is', 'was', 'are', 'were', 'been', 'be', 'have', 
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 
            'might', 'must', 'can', 'what', 'when', 'where', 'how', 'why', 'who'
        }
        
        words = [
            word for word in re.findall(r'\w+', all_text)
            if len(word) > 2 and word not in stop_words
        ]
        
        return list(set(words))
    
    def _calculate_summary_length(self, source_count: int) -> int:
        """
        Calculate optimal summary length based on number of sources.
        
        Args:
            source_count: Number of sources to process
            
        Returns:
            Target summary length in characters
        """
        if source_count <= 3:
            return self.MAX_CHARS_PER_SOURCE
        elif source_count <= 6:
            return max(self.MIN_CHARS_PER_SOURCE, self.MAX_TOTAL_CHARS // source_count)
        else:
            return self.MIN_CHARS_PER_SOURCE
    
    def _find_relevant_sections(
        self,
        content: str,
        keywords: List[str],
        max_sections: int = 3
    ) -> List[str]:
        """
        Find the most relevant sections of content based on keyword matches.
        
        Args:
            content: The source content to analyze
            keywords: List of keywords to search for
            max_sections: Maximum number of sections to return
            
        Returns:
            List of relevant content sections
        """
        if not keywords:
            return [content[:self.MAX_CHARS_PER_SOURCE]]
        
        sections = []
        content_lower = content.lower()
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            start = 0
            
            while True:
                pos = content_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                
                section_start = max(0, pos - self.CONTEXT_WINDOW_SIZE)
                section_end = min(len(content), pos + len(keyword) + self.CONTEXT_WINDOW_SIZE)
                
                section = content[section_start:section_end].strip()
                if len(section) > 50:  # Only include substantial sections
                    sections.append(section)
                
                start = pos + 1
                
                if len(sections) >= max_sections:
                    break
            
            if len(sections) >= max_sections:
                break
        
        if not sections:
            return [content[:self.MAX_CHARS_PER_SOURCE]]
        
        return sections[:max_sections]

    def _calculate_relevance_score(
        self,
        unique_keywords_found: int,
        total_keyword_matches: int,
        total_keywords: int,
        content_length: int
    ) -> float:
        """
        Calculate relevance score based on keyword matches.

        Args:
            unique_keywords_found: Number of unique keywords found
            total_keyword_matches: Total number of keyword matches
            total_keywords: Total number of keywords searched for
            content_length: Length of the content

        Returns:
            Relevance score between 0 and 1
        """
        coverage = unique_keywords_found / total_keywords if total_keywords > 0 else 0

        density = (total_keyword_matches / content_length) * 1000 if content_length > 0 else 0

        normalized_density = min(density / 10, 1)

        return (coverage * 0.7) + (normalized_density * 0.3)

    def _extract_relevant_sections(
        self,
        content: str,
        keyword_positions: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Extract relevant sections around keyword matches.

        Args:
            content: The full content text
            keyword_positions: List of keyword positions with 'keyword' and 'position' keys

        Returns:
            List of extracted content sections
        """
        if not keyword_positions:
            return [content[:self.MIN_CHARS_PER_SOURCE]]

        keyword_positions.sort(key=lambda x: x['position'])

        windows = []

        for pos_info in keyword_positions:
            position = pos_info['position']
            start = max(0, position - self.CONTEXT_WINDOW_SIZE)
            end = min(len(content), position + self.CONTEXT_WINDOW_SIZE)

            if windows and start <= windows[-1]['end']:
                windows[-1]['end'] = end
            else:
                windows.append({'start': start, 'end': end})

        sections = []

        for window in windows:
            start = window['start']
            end = window['end']

            prev_period = content.rfind('.', 0, start)
            prev_newline = content.rfind('\n', 0, start)
            start = max(prev_period + 1, prev_newline + 1, 0)

            next_period = content.find('.', end)
            next_newline = content.find('\n', end)

            if next_period != -1 or next_newline != -1:
                end = min(
                    next_period + 1 if next_period != -1 else len(content),
                    next_newline if next_newline != -1 else len(content)
                )

            section = content[start:end].strip()
            if section:
                sections.append(section)

        return sections

    async def _summarize_source(
        self,
        source: Dict[str, Any],
        query: str,
        search_queries: List[str],
        target_length: int,
        on_progress: Optional[Callable[[str, Optional[str]], None]] = None
    ) -> ProcessedSource:
        """
        Summarize a single source using GPT-4o-mini.

        Args:
            source: Source dictionary with content to summarize
            query: Original user query
            search_queries: List of generated search queries
            target_length: Target length for summary
            on_progress: Optional progress callback

        Returns:
            ProcessedSource with AI-generated summary
        """
        content = source.get('content', '')

        if not content or len(content) < 100:
            return ProcessedSource(
                title=source.get('title', ''),
                url=source.get('url', ''),
                content='',
                score=source.get('score', 0.0),
                published_date=source.get('published_date', ''),
                relevance_score=0,
                extracted_sections=[],
                keywords=[],
                summarized=False
            )

        try:
            content_to_analyze = content[:15000]
            if len(content) > 15000:
                content_to_analyze += '\n[... content truncated]'

            prompt = f"""You are a research assistant helping to extract the most relevant information from a webpage.

User's question: "{query}"
Related search queries: {', '.join(search_queries)}

Source title: {source.get('title', '')}
Source URL: {source.get('url', '')}

Content to analyze:
{content_to_analyze}

Instructions:
1. Extract ONLY the information that directly relates to the user's question and search queries
2. Focus on specific facts, data, quotes, and concrete details
3. Preserve important numbers, dates, names, and technical details
4. Maintain the original meaning and context
5. If the content has little relevance to the query, just note that briefly
6. Target length: approximately {target_length} characters

Provide a focused summary that would help answer the user's question:"""

            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=max(100, target_length // 3)  # Rough token estimation
            )

            summary = response.choices[0].message.content.strip()

            relevance_score = self._calculate_relevance_from_summary(summary, query, search_queries)

            return ProcessedSource(
                title=source.get('title', ''),
                url=source.get('url', ''),
                content=summary,
                score=source.get('score', 0.0),
                published_date=source.get('published_date', ''),
                relevance_score=relevance_score,
                extracted_sections=[summary],
                keywords=self._extract_keywords(query, search_queries),
                summarized=True
            )

        except Exception as error:
            print(f"Failed to summarize source {source.get('url', '')}: {error}")

            keywords = self._extract_keywords(query, search_queries)
            processed = await self._process_source(source, keywords)

            fallback_sources = self._distribute_character_budget([processed])
            return fallback_sources[0] if fallback_sources else processed

    def _calculate_relevance_from_summary(
        self,
        summary: str,
        query: str,
        search_queries: List[str]
    ) -> float:
        """
        Calculate relevance score from summary.

        Args:
            summary: AI-generated summary
            query: Original user query
            search_queries: List of generated search queries

        Returns:
            Relevance score between 0 and 1
        """
        summary_length = len(summary)

        low_relevance_phrases = [
            'not directly related',
            'no specific information',
            "doesn't mention",
            'no relevant content',
            'unrelated to'
        ]

        summary_lower = summary.lower()
        has_low_relevance = any(phrase in summary_lower for phrase in low_relevance_phrases)

        if has_low_relevance:
            return 0.1  # Very low relevance

        high_relevance_indicators = [
            'specifically mentions',
            'directly addresses',
            'provides detailed',
            'explains how',
            'data shows',
            'research indicates'
        ]

        has_high_relevance = any(phrase in summary_lower for phrase in high_relevance_indicators)

        score = min(summary_length / 2000, 1.0)  # Base score from length

        if has_high_relevance:
            score = min(score + 0.3, 1.0)

        keywords = self._extract_keywords(query, search_queries)
        keyword_matches = sum(
            1 for keyword in keywords
            if keyword.lower() in summary_lower
        )

        keyword_score = keyword_matches / len(keywords) if keywords else 0.5

        return (score * 0.6) + (keyword_score * 0.4)

    async def _process_source(self, source: Dict[str, Any], keywords: List[str]) -> ProcessedSource:
        """Process a source without AI summarization (fallback method)."""
        content = source.get('content', '')

        if not content:
            return ProcessedSource(
                title=source.get('title', ''),
                url=source.get('url', ''),
                content='',
                score=source.get('score', 0.0),
                published_date=source.get('published_date', ''),
                relevance_score=0,
                extracted_sections=[],
                keywords=[]
            )

        content_lower = content.lower()
        keyword_positions = []
        found_keywords = []

        for keyword in keywords:
            keyword_lower = keyword.lower()
            start = 0
            while True:
                pos = content_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                keyword_positions.append({'keyword': keyword, 'position': pos})
                if keyword not in found_keywords:
                    found_keywords.append(keyword)
                start = pos + 1

        relevance_score = self._calculate_relevance_score(
            len(found_keywords),
            len(keyword_positions),
            len(keywords),
            len(content)
        )

        extracted_sections = self._extract_relevant_sections(content, keyword_positions)

        return ProcessedSource(
            title=source.get('title', ''),
            url=source.get('url', ''),
            content=content,
            score=source.get('score', 0.0),
            published_date=source.get('published_date', ''),
            relevance_score=relevance_score,
            extracted_sections=extracted_sections,
            keywords=found_keywords
        )

    def _distribute_character_budget(
        self,
        sources: List[ProcessedSource]
    ) -> List[ProcessedSource]:
        """
        Distribute character budget among sources based on relevance.

        Args:
            sources: List of processed sources

        Returns:
            List of sources with content trimmed to fit budget
        """
        relevant_sources = [s for s in sources if s.relevance_score > 0]

        if not relevant_sources:
            return [
                ProcessedSource(
                    title=s.title,
                    url=s.url,
                    content=s.content[:self.MAX_CHARS_PER_SOURCE],
                    score=s.score,
                    published_date=s.published_date,
                    relevance_score=s.relevance_score,
                    extracted_sections=s.extracted_sections,
                    keywords=s.keywords,
                    summarized=s.summarized
                )
                for s in sources[:5]
            ]

        total_relevance = sum(s.relevance_score for s in relevant_sources)

        remaining_budget = self.MAX_TOTAL_CHARS
        processed_results = []

        for source in relevant_sources:
            if remaining_budget <= 0:
                break

            relevance_ratio = source.relevance_score / total_relevance
            allocated_chars = int(relevance_ratio * self.MAX_TOTAL_CHARS)

            target_chars = max(
                self.MIN_CHARS_PER_SOURCE,
                min(allocated_chars, self.MAX_CHARS_PER_SOURCE, remaining_budget)
            )

            if source.extracted_sections:
                processed_content = '\n\n[...]\n\n'.join(source.extracted_sections)

                if len(processed_content) < target_chars and source.content:
                    additional_content = source.content[:target_chars - len(processed_content)]
                    processed_content = additional_content + '\n\n[...]\n\n' + processed_content
            else:
                processed_content = source.content[:target_chars]

            if len(processed_content) > target_chars:
                processed_content = processed_content[:target_chars] + '\n[... content truncated]'

            remaining_budget -= len(processed_content)

            processed_results.append(ProcessedSource(
                title=source.title,
                url=source.url,
                content=processed_content,
                score=source.score,
                published_date=source.published_date,
                relevance_score=source.relevance_score,
                extracted_sections=source.extracted_sections,
                keywords=source.keywords,
                summarized=source.summarized
            ))

        return processed_results
