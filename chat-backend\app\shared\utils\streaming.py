"""
Streaming utilities for workflows.
"""

import json
from typing import Dict, Any, AsyncGenerator, List
from datetime import datetime

from ..models.base import <PERSON>t<PERSON><PERSON>po<PERSON>, SearchResult


class StreamingUtils:
    """Utilities for streaming responses."""
    
    @staticmethod
    def format_sse_data(data: Dict[str, Any]) -> str:
        """Format data for Server-Sent Events."""
        return f"data: {json.dumps(data)}\n\n"
    
    @staticmethod
    def format_status_event(status: str, step: str = None) -> str:
        """Format a status update event."""
        event_data = {"type": "status", "status": status}
        if step:
            event_data["step"] = step
        return StreamingUtils.format_sse_data(event_data)
    
    @staticmethod
    def format_content_event(content: str) -> str:
        """Format a content chunk event."""
        return StreamingUtils.format_sse_data({
            "type": "content", 
            "data": content
        })
    
    @staticmethod
    def format_final_event(response: ChatResponse) -> str:
        """Format the final response event."""
        return StreamingUtils.format_sse_data({
            "type": "final", 
            "data": response.model_dump()
        })
    
    @staticmethod
    def format_error_event(error_message: str) -> str:
        """Format an error event."""
        return StreamingUtils.format_sse_data({
            "type": "error", 
            "message": error_message
        })
    
    @staticmethod
    def format_done_event() -> str:
        """Format the done event."""
        return "data: [DONE]\n\n"
    
    @staticmethod
    def serialize_messages(messages) -> List[Dict[str, str]]:
        """Serialize LangChain messages for JSON response."""
        from langchain_core.messages import HumanMessage, AIMessage
        
        serialized = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                serialized.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                serialized.append({"role": "assistant", "content": msg.content})
        return serialized
