"""
Understanding node for advanced search workflow.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from langchain_core.messages import SystemMessage, HumanMessage

from ..state import SearchState
from ....shared.base.node_base import BaseNode
from ....shared.services import LLMService


class UnderstandNode(BaseNode):
    """Node for analyzing user query to understand intent."""
    
    def __init__(self):
        super().__init__("understand")
        self.llm_service = LLMService()
    
    async def execute(
        self, 
        state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Understanding phase - analyze the user query."""
        self.log_entry(state)
        
        try:
            understanding = await self._analyze_query(state["query"], state.get("context"))
            
            result = {
                "understanding": understanding,
                "phase": "planning"
            }
            
            self.log_exit(state, result)
            return result
            
        except Exception as error:
            self.logger.error(f"Understanding phase failed: {str(error)}")
            return {
                "error": str(error),
                "error_type": "llm",
                "phase": "error"
            }
    
    async def _analyze_query(self, query: str, context: Optional[List[Dict[str, str]]] = None) -> str:
        """Analyze the user query to understand intent."""
        context_str = ""
        if context:
            context_str = "\n".join([
                f"User: {c.get('query', '')}\nAssistant: {c.get('response', '')}"
                for c in context[-3:]  # Last 3 exchanges
            ])
        
        messages = [
            SystemMessage(content=f"""You are analyzing a user's search query to understand their intent and information needs.

{self._get_current_date_context()}

Previous conversation context:
{context_str}

Analyze the query and provide:
1. The main topic or subject
2. What specific information they're looking for
3. The type of answer they expect (factual, explanatory, comparative, etc.)
4. Any time-sensitive aspects
5. The level of detail needed

Be concise but thorough in your analysis."""),
            HumanMessage(content=f"Query to analyze: {query}")
        ]
        
        llm = self.llm_service.get_fast_llm()
        response = await llm.ainvoke(messages)
        return response.content
    
    def _get_current_date_context(self) -> str:
        """Get current date context for queries."""
        now = datetime.now()
        return f"Today is {now.strftime('%A, %B %d, %Y')}. Current time: {now.strftime('%I:%M %p')} UTC."
