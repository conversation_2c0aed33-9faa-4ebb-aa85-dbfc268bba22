"""
Source-related data models.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional


@dataclass
class Source:
    """Basic source information from search providers."""
    title: str
    url: str
    content: str
    score: float = 0.0
    published_date: str = ""


@dataclass
class ProcessedSource:
    """Processed source with relevance scoring and extracted sections."""
    title: str
    url: str
    content: str
    score: float
    published_date: str
    relevance_score: float
    extracted_sections: List[str]
    keywords: List[str]
    summarized: bool = False


@dataclass
class SubQuery:
    """Sub-query for advanced search planning."""
    query: str
    reasoning: str
    priority: int = 1


@dataclass
class SearchStep:
    """Search step for UI progress tracking."""
    id: str
    label: str
    status: str = 'pending'  # pending, active, complete, error
