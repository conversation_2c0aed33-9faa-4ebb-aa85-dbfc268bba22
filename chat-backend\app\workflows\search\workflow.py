"""
Advanced search workflow with 6-phase processing.
"""

from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, START, END
from langgraph.graph.state import CompiledStateGraph

from .state import SearchState
from .nodes import (
    UnderstandNode, PlanNode, SearchNode, ScrapeNode, 
    AnalyzeNode, SynthesizeNode, ErrorHandlerNode,
    search_routing, scrape_routing, analyze_routing, 
    synthesize_routing, error_routing
)
from ...shared.base.workflow_base import BaseWorkflow
from ...shared.config import SEARCH_CONFIG


class AdvancedSearchWorkflow(BaseWorkflow[SearchState]):
    """
    Advanced LangGraph-based search engine with 6-phase processing:
    1. Understanding - Analyze the user query
    2. Planning - Generate search strategies and sub-queries
    3. Searching - Execute searches with multiple providers
    4. Scraping - Gather additional content from sources
    5. Analyzing - Process and score content relevance
    6. Synthesizing - Generate comprehensive streaming answer
    """
    
    def __init__(self, options: Optional[Dict[str, Any]] = None):
        # Initialize nodes
        self.understand_node = UnderstandNode()
        self.plan_node = PlanNode()
        self.search_node = SearchNode()
        self.scrape_node = ScrapeNode()
        self.analyze_node = AnalyzeNode()
        self.synthesize_node = SynthesizeNode()
        self.error_handler_node = ErrorHandlerNode()
        
        super().__init__(options)
    
    def _build_graph(self) -> CompiledStateGraph:
        """Build the advanced search workflow graph."""
        workflow = StateGraph(SearchState)
        
        # Add nodes
        workflow.add_node("understand", self.understand_node.execute)
        workflow.add_node("plan", self.plan_node.execute)
        workflow.add_node("search", self.search_node.execute)
        workflow.add_node("scrape", self.scrape_node.execute)
        workflow.add_node("analyze", self.analyze_node.execute)
        workflow.add_node("synthesize", self.synthesize_node.execute)
        workflow.add_node("handle_error", self.error_handler_node.execute)
        workflow.add_node("complete", lambda state: {"phase": "complete"})
        
        # Set entry point
        workflow.add_edge(START, "understand")
        
        # Add conditional edges
        workflow.add_conditional_edges(
            "understand",
            lambda state: "handle_error" if state.get("phase") == "error" else "plan",
            {"handle_error": "handle_error", "plan": "plan"}
        )
        
        workflow.add_conditional_edges(
            "plan",
            lambda state: "handle_error" if state.get("phase") == "error" else "search",
            {"handle_error": "handle_error", "search": "search"}
        )
        
        workflow.add_conditional_edges(
            "search",
            search_routing,
            {"handle_error": "handle_error", "scrape": "scrape"}
        )
        
        workflow.add_conditional_edges(
            "scrape",
            scrape_routing,
            {"handle_error": "handle_error", "analyze": "analyze"}
        )
        
        workflow.add_conditional_edges(
            "analyze",
            analyze_routing,
            {"handle_error": "handle_error", "synthesize": "synthesize"}
        )
        
        workflow.add_conditional_edges(
            "synthesize",
            synthesize_routing,
            {"handle_error": "handle_error", "complete": "complete"}
        )
        
        workflow.add_conditional_edges(
            "handle_error",
            error_routing,
            {
                "understand": "understand",
                "search": "search", 
                "analyze": "analyze",
                "complete": "complete"
            }
        )
        
        # Complete node goes to END
        workflow.add_edge("complete", END)
        
        return workflow.compile()
    
    async def execute(
        self, 
        initial_state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute the advanced search workflow."""
        return self.graph.invoke(initial_state, config)
    
    async def stream(
        self, 
        initial_state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ):
        """Stream the workflow execution."""
        async for chunk in self.graph.astream(initial_state, config, stream_mode=["updates", "custom"]):
            yield chunk
    
    def get_supported_operations(self) -> list[str]:
        """Get list of operations this workflow supports."""
        return ["execute", "stream"]
