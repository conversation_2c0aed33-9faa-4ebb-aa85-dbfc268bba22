"""
Chat session management utilities.
"""

from typing import Dict, List
from langchain_core.messages import BaseMessage


class SessionManager:
    """Manages chat sessions across workflows."""
    
    def __init__(self):
        self.sessions: Dict[str, List[BaseMessage]] = {}
    
    def get_session(self, session_id: str) -> List[BaseMessage]:
        """Get messages for a session."""
        return self.sessions.get(session_id, [])
    
    def add_message(self, session_id: str, message: BaseMessage) -> None:
        """Add a message to a session."""
        if session_id not in self.sessions:
            self.sessions[session_id] = []
        self.sessions[session_id].append(message)
    
    def update_session(self, session_id: str, messages: List[BaseMessage]) -> None:
        """Update entire session with new messages."""
        self.sessions[session_id] = messages
    
    def clear_session(self, session_id: str) -> bool:
        """Clear a session. Returns True if session existed."""
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return list(self.sessions.keys())
    
    def session_exists(self, session_id: str) -> bool:
        """Check if a session exists."""
        return session_id in self.sessions
