"""
Search node for advanced search workflow.
"""

from typing import Dict, Any, Optional

from ..state import SearchState
from ....shared.base.node_base import BaseNode
from ....shared.services.search_providers import TavilySearchProvider
from ....shared.models.sources import Source


class SearchNode(BaseNode):
    """Node for executing search queries."""
    
    def __init__(self):
        super().__init__("search")
        self.tavily_provider = TavilySearchProvider()
    
    async def execute(
        self, 
        state: SearchState, 
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Search phase - execute search queries."""
        self.log_entry(state)
        
        try:
            search_queries = state.get("search_queries", [])
            sources = []
            
            for query in search_queries:
                try:
                    search_response = self.tavily_provider.search(query, limit=6)
                    
                    for result in search_response.get("results", []):
                        source = Source(
                            title=result.get("title", ""),
                            url=result.get("url", ""),
                            content=result.get("content", ""),
                            score=result.get("score", 0.0),
                            published_date=result.get("published_date", "")
                        )
                        sources.append(source)
                        
                except Exception as e:
                    self.logger.warning(f"Search failed for query '{query}': {str(e)}")
                    continue
            
            result = {
                "sources": sources,
                "phase": "analyzing"
            }
            
            self.log_exit(state, result)
            return result
            
        except Exception as error:
            self.logger.error(f"Search phase failed: {str(error)}")
            return {
                "error": str(error),
                "error_type": "search",
                "phase": "error"
            }
