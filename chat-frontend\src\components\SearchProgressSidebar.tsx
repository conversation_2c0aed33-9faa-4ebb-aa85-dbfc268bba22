/**
 * SearchProgressSidebar - Modern progress visualization for advanced search
 * Shows real-time search progress with phase indicators, progress bars, and animations
 */

import React, { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  SearchPhase, 
  SearchEvent, 
  SearchStep, 
  Source
} from '@/types/search';
import { 
  Search, 
  Brain, 
  Globe, 
  FileText, 
  Zap, 
  CheckCircle, 
  AlertCircle,
  Clock,
  ExternalLink,
  Loader2
} from 'lucide-react';

interface SearchProgressSidebarProps {
  isVisible: boolean;
  events: SearchEvent[];
  currentPhase: SearchPhase;
  sources: Source[];
  isSearching: boolean;
  error?: string;
}

const phaseConfig = {
  understanding: {
    icon: Brain,
    label: 'Understanding',
    color: 'bg-blue-500',
    description: 'Analyzing your query'
  },
  planning: {
    icon: Search,
    label: 'Planning',
    color: 'bg-purple-500',
    description: 'Creating search strategy'
  },
  searching: {
    icon: Globe,
    label: 'Searching',
    color: 'bg-green-500',
    description: 'Finding relevant sources'
  },
  analyzing: {
    icon: FileText,
    label: 'Analyzing',
    color: 'bg-orange-500',
    description: 'Processing content'
  },
  synthesizing: {
    icon: Zap,
    label: 'Synthesizing',
    color: 'bg-yellow-500',
    description: 'Generating response'
  },
  complete: {
    icon: CheckCircle,
    label: 'Complete',
    color: 'bg-emerald-500',
    description: 'Search finished'
  },
  error: {
    icon: AlertCircle,
    label: 'Error',
    color: 'bg-red-500',
    description: 'Something went wrong'
  }
};

const SearchProgressSidebar: React.FC<SearchProgressSidebarProps> = ({
  isVisible,
  events,
  currentPhase,
  sources,
  isSearching,
  error
}) => {
  const [steps, setSteps] = useState<SearchStep[]>([]);
  const [animatingPhase, setAnimatingPhase] = useState<SearchPhase | null>(null);

  useEffect(() => {
    const initialSteps: SearchStep[] = [
      { id: 'understanding', label: 'Understanding', status: 'pending' },
      { id: 'planning', label: 'Planning', status: 'pending' },
      { id: 'searching', label: 'Searching', status: 'pending' },
      { id: 'analyzing', label: 'Analyzing', status: 'pending' },
      { id: 'synthesizing', label: 'Synthesizing', status: 'pending' },
    ];
    setSteps(initialSteps);
  }, []);

  useEffect(() => {
    setSteps(prevSteps => 
      prevSteps.map(step => {
        const phaseOrder = ['understanding', 'planning', 'searching', 'analyzing', 'synthesizing'];
        const currentIndex = phaseOrder.indexOf(currentPhase);
        const stepIndex = phaseOrder.indexOf(step.id);
        
        if (stepIndex < currentIndex) {
          return { ...step, status: 'completed' as const };
        } else if (stepIndex === currentIndex) {
          return { ...step, status: 'active' as const, startTime: Date.now() };
        } else {
          return { ...step, status: 'pending' as const };
        }
      })
    );

    if (currentPhase !== 'complete' && currentPhase !== 'error') {
      setAnimatingPhase(currentPhase);
      setTimeout(() => setAnimatingPhase(null), 500);
    }
  }, [currentPhase]);

  const getPhaseProgress = () => {
    const phaseOrder = ['understanding', 'planning', 'searching', 'analyzing', 'synthesizing'];
    const currentIndex = phaseOrder.indexOf(currentPhase);
    return currentPhase === 'complete' ? 100 : ((currentIndex + 1) / phaseOrder.length) * 100;
  };

  const getRecentEvents = () => {
    return events.slice(-10).reverse();
  };

  if (!isVisible) return null;

  return (
    <div className="w-80 border-l bg-white flex flex-col">
      <div className="p-4 border-b">
        <div className="flex items-center gap-2">
          <Search className="h-5 w-5 text-blue-500" />
          <h2 className="font-semibold text-lg">Search Progress</h2>
        </div>
        <Progress value={getPhaseProgress()} className="mt-2" />
      </div>

      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full p-4">
          {/* Current Phase */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Current Phase</h3>
            <div>
              <Card className={`transition-all duration-300 ${animatingPhase === currentPhase ? 'scale-105 shadow-lg' : ''}`}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    {React.createElement(phaseConfig[currentPhase]?.icon || Clock, {
                      className: `h-6 w-6 ${isSearching ? 'animate-spin' : ''} text-white`
                    })}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{phaseConfig[currentPhase]?.label}</span>
                        {isSearching && <Loader2 className="h-4 w-4 animate-spin" />}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {phaseConfig[currentPhase]?.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Phase Steps */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Progress Steps</h3>
            <div className="space-y-2">
                {steps.map((step) => {
                  const config = phaseConfig[step.id as SearchPhase];
                  const Icon = config?.icon || Clock;
                  
                  return (
                    <div key={step.id} className="flex items-center gap-3 p-2 rounded-md transition-colors">
                        <div className={`
                          flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300
                          ${step.status === 'completed' ? 'bg-green-500 text-white' : 
                            step.status === 'active' ? `${config?.color} text-white animate-pulse` : 
                            'bg-gray-200 text-gray-500'}
                        `}>
                          {step.status === 'completed' ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <Icon className={`h-4 w-4 ${step.status === 'active' ? 'animate-pulse' : ''}`} />
                          )}
                        </div>
                        <div className="flex-1">
                          <span className={`text-sm font-medium ${
                            step.status === 'active' ? 'text-foreground' : 
                            step.status === 'completed' ? 'text-green-600' : 
                            'text-muted-foreground'
                          }`}>
                            {step.label}
                          </span>
                        </div>
                        <Badge variant={
                          step.status === 'completed' ? 'default' :
                          step.status === 'active' ? 'secondary' : 'outline'
                        }>
                          {step.status}
                        </Badge>
                      </div>
                  );
                })}
            </div>
          </div>

          <Separator className="my-4" />

          {/* Sources Found */}
          {sources.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Sources Found ({sources.length})</h3>
              <div className="space-y-2">
                  {sources.slice(0, 5).map((source, index) => (
                    <div key={index}>
                      <Card className="transition-all duration-200 hover:shadow-md">
                        <CardContent className="p-3">
                          <div className="flex items-start gap-2">
                            <Globe className="h-4 w-4 mt-1 text-blue-500 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <h4 className="text-sm font-medium truncate" title={source.title}>
                                {source.title}
                              </h4>
                              <p className="text-xs text-muted-foreground truncate" title={source.url}>
                                {source.url}
                              </p>
                              {source.summary && (
                                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                  {source.summary}
                                </p>
                              )}
                            </div>
                            <ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  ))}
              </div>
              {sources.length > 5 && (
                <p className="text-xs text-muted-foreground text-center mt-2">
                  +{sources.length - 5} more sources
                </p>
              )}
            </div>
          )}

          {/* Recent Events */}
          {events.length > 0 && (
            <>
              <Separator className="my-4" />
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Activity</h3>
                <div className="space-y-2">
                    {getRecentEvents().map((event, index) => (
                      <div key={index} className="flex items-start gap-2 p-2 text-xs">
                          <Clock className="h-3 w-3 mt-0.5 text-muted-foreground flex-shrink-0" />
                          <div className="flex-1">
                            <span className="text-muted-foreground">
                              {event.type === 'phase-update' && event.message}
                              {event.type === 'thinking' && `💭 ${event.message}`}
                              {event.type === 'searching' && `🔍 Searching: ${event.query}`}
                              {event.type === 'found' && `✅ Found ${event.sources?.length} sources`}
                              {event.type === 'scraping' && `📄 Processing: ${event.url}`}
                              {event.type === 'content-chunk' && `📝 Generating response...`}
                              {event.type === 'error' && `❌ Error: ${event.error}`}
                            </span>
                          </div>
                        </div>
                    ))}
                </div>
              </div>
            </>
          )}

          {/* Error Display */}
          {error && (
            <>
              <Separator className="my-4" />
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Error</h3>
                <div>
                  <Card className="border-red-200 bg-red-50">
                    <CardContent className="p-3">
                      <div className="flex items-start gap-2">
                        <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0 mt-0.5" />
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </>
          )}
        </ScrollArea>
      </div>
    </div>
  );
};

export default SearchProgressSidebar;
