"""
Base data models shared across workflows.
"""

from pydantic import BaseModel
from typing import List, Dict, Any, Optional


class ChatMessage(BaseModel):
    """User chat message input."""
    message: str
    session_id: str | None = None


class SearchResult(BaseModel):
    """Search result from any search provider."""
    title: str
    url: str
    content: str


class ChatResponse(BaseModel):
    """AI chat response with optional search information."""
    response: str
    session_id: str
    search_performed: bool = False
    search_query: Optional[str] = None
    search_results: List[SearchResult] = []


class ChatHistory(BaseModel):
    """Chat history for a session."""
    messages: List[Dict[str, Any]]
    session_id: str
